import nrc_interface as aa
import sys
from PyQt5.QtWidgets import QApplication
from Widget import WidgetApp  # 从 test1.py 导入 WidgetApp 类
import time
from PyQt5.QtCore import pyqtSignal , QThread
import threading


Size = 0
pos = aa.DoubleVector()

def connect(ip, port):
    fd = aa.connect_robot(ip, port)
    print("初始化控制器ID: ", fd)
    return fd

def set_globe(socketFd):
    posInfo = aa.DoubleVector()
    posInfo = [0, 0, 0, 0, 0, 0, 0, 10, 20, 30, 0, 0, 0, 0]
    name = "GP0001"
    result = aa.set_global_position(socketFd, name, posInfo)
    print(result)

def clear_error(socketFd, window):
    aa.clear_error(socketFd)
    window.update_edit1_text('清错')

def power_ctr( socketFd, name, window):
    if name == 'on':
        aa.set_servo_poweron(socketFd)
        window.update_edit1_text('上使能')
    elif name == 'off':
        aa.set_servo_poweroff(socketFd)
        window.update_edit1_text('下使能')
    else:
        print('error')
    

def queue_cmd(socketFd, name, window):
    global Size
    global pos
    # 确保线程在主线程退出前完成
    if name == 'star':
        result = aa.queue_motion_set_status(socketFd, True)
    elif name == 'movj':
        cmd = get_pos(window)
        cmd.velocity = 80
        cmd.acc = 100
        cmd.dec = 100
        print('cmd: ', list(cmd.targetPosValue))
        result = aa.queue_motion_push_back_moveJ(socketFd, cmd)
        if result == 0:
            Size += 1
        else:
            print('指令插入失败')
        # 更新窗口的 QTextEdit 文本
        window.update_edit_text("movJ")
    elif name == 'movl':
        cmd = get_pos(window)
        cmd.velocity = 80
        cmd.acc = 100
        cmd.dec = 100
        print('cmd: ', list(cmd.targetPosValue))
        result = aa.queue_motion_push_back_moveL(socketFd, cmd)
        print(result)
        if result == 0:
            Size += 1
        else:
            print('指令插入失败')
        # 更新窗口的 QTextEdit 文本
        window.update_edit_text("movL")
    elif name == 'send':
        result =  aa.queue_motion_send_to_controller(socketFd, Size)
        print('运动队列长度: ', Size)
        time.sleep(0.5)
        Size = 0
        jug = 1
        while jug != 0:
            #status的类似是一个int类型，获取到状态之后status会变成一个列表，所有再次调用接口的时候需要重新定义status为int类型传入
            status = 1
            status = aa.get_robot_running_state(socketFd, status) 
            jug = status[1]
        time.sleep(0.5)
        window.clear_edit_text()


class get_status(QThread):
    update_label_signal = pyqtSignal(str)  # 定义一个信号用于更新 QLabel 文本

    def __init__(self, socketFd):
        super().__init__()
        self.socketFd = socketFd


    def run(self):
        while True:
            time.sleep(1)
            servo_status(self)
            get_position()




def servo_status(self):
    status = 0
    status = aa.get_servo_state(self.socketFd, status)
    #接口获取到的值返回的是一个列表，例如：这里的status[0]表示接口的返回值，status[1]表示当前的伺服状态
    judge = status[1]
    if judge == 0:
        self.update_label_signal.emit('伺服停止')
    elif judge == 1:
        self.update_label_signal.emit('伺服就绪')
    elif judge == 2:
        self.update_label_signal.emit('伺服报警')
    elif judge == 3:
        self.update_label_signal.emit('伺服运行')
    else:
        print('error retuen : ', judge)

            
def get_pos(window):
    # 从文本框获取值并转换为浮点数
    time.sleep(1.5)
    pos = aa.DoubleVector()
    axis = [
        float(window.get_axis1_text() or '0'),
        float(window.get_axis2_text() or '0'),
        float(window.get_axis3_text() or '0'),
        float(window.get_axis4_text() or '0'),
        float(window.get_axis5_text() or '0'),
        float(window.get_axis6_text() or '0')
    ]

    # 创建并填充 DoubleVector
    for value in axis:
        pos.append(value)  # 使用 append 方法逐个添加值

    cmd = aa.MoveCmd()
    cmd.targetPosType = aa.PosType_data
    cmd.targetPosValue = pos
    print('cmd:', list(cmd.targetPosValue))
    
    return cmd

def get_position():
    pos = aa.DoubleVector()
    coord = 0  # 获取关节坐标
    aa.get_current_position(socketFd, coord, pos)
    
    # 将 pos 中的值分别设置到相应的文本框
    for i in range(6):
        getattr(window, f'set_point{i+1}_text')(pos[i])

if __name__ == "__main__":
    socketFd = connect("192.168.1.13", "6001")
    if socketFd > 0:
        
        # 创建应用程序实例
        app = QApplication(sys.argv)

        # 创建并显示窗口
        window = WidgetApp(socketFd)
        window.show()

        #创建线程获取伺服状态
        thread = get_status(socketFd)
        thread.update_label_signal.connect(window.set_label_text)  # 连接信号与槽函数
        thread.start()

        # 进入应用程序主循环
        sys.exit(app.exec_())
        
    #等待线程结束
    thread.join()
