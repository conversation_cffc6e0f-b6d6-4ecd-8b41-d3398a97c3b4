#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队列模式G-code执行程序
创建一个简单的测试G-code文件并验证队列模式功能
"""

import os
import sys

def create_test_gcode():
    """创建一个简单的测试G-code文件"""
    test_gcode_content = """
; 测试G-code文件 - 简单的正方形路径
; 起始点
G1 X0.0 Y0.0 Z10.0
; 正方形路径
G1 X10.0 Y0.0 Z10.0
G1 X10.0 Y10.0 Z10.0
G1 X0.0 Y10.0 Z10.0
G1 X0.0 Y0.0 Z10.0
; 结束点
G1 X0.0 Y0.0 Z20.0
"""
    
    # 保存到test目录下
    test_file_path = os.path.join(os.path.dirname(__file__), "test_square.gcode")
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_gcode_content.strip())
    
    print(f"✅ 测试G-code文件已创建: {test_file_path}")
    return test_file_path

def test_queue_mode():
    """测试队列模式功能"""
    print("=" * 60)
    print("队列模式G-code执行测试")
    print("=" * 60)
    
    # 1. 创建测试G-code文件
    test_file = create_test_gcode()
    
    # 2. 修改run_gcode.py中的文件路径
    run_gcode_path = os.path.join(os.path.dirname(__file__), "run_gcode.py")
    
    print(f"\n📝 要测试队列模式，请:")
    print(f"1. 修改 {run_gcode_path} 中的 GCODE_FILE 变量")
    print(f"   将其设置为: \"test_square.gcode\"")
    print(f"2. 确保机械臂已连接并配置正确")
    print(f"3. 运行: python {run_gcode_path}")
    
    print(f"\n🎯 队列模式特点:")
    print("- 所有路径点会批量添加到队列")
    print("- 机械臂将平滑连续执行所有运动")
    print("- 减少运动间的停顿，提高执行效率")
    print("- 适合连续路径如3D打印、激光切割等应用")
    
    print(f"\n📊 测试文件信息:")
    print(f"- 文件路径: {test_file}")
    print("- 路径类型: 简单正方形")
    print("- 路径点数: 6个")
    print("- 坐标范围: X(0-10), Y(0-10), Z(10-20)")

if __name__ == "__main__":
    test_queue_mode()
