#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的队列模式测试 - 使用关节坐标
基于官方demo的正确实现方式
"""

import sys
import os
import time
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def test_queue_with_joint_coords():
    """使用关节坐标测试队列模式"""
    print("=" * 60)
    print("正确的队列模式测试 - 关节坐标")
    print("=" * 60)
    
    socket_fd = -1
    try:
        # 1. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 2. 上电
        print("🔌 正在上电...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败，错误码: {result}")
            return
        print("✅ 上电成功")
        time.sleep(2)

        # 3. 获取当前关节角度
        print("📍 获取当前关节角度...")
        joint_pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 0, joint_pos)  # 0=关节坐标
        if result != 0 or len(joint_pos) < 6:
            print(f"❌ 获取关节角度失败，错误码: {result}")
            return
        
        current_joints = [joint_pos[i] for i in range(6)]
        current_joints_deg = [math.degrees(angle) for angle in current_joints]
        print(f"   当前关节角度: {[f'{angle:.1f}°' for angle in current_joints_deg]}")

        # 4. 设置远程模式
        print("🔧 设置远程模式...")
        result = nrc.set_current_mode(socket_fd, 1)
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return
        print("✅ 远程模式设置成功")

        # 5. 启动队列模式
        print("🚀 启动队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启动队列模式失败，错误码: {result}")
            return
        print("✅ 队列模式启动成功")

        # 6. 清除队列数据
        print("🧹 清除队列数据...")
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return
        print("✅ 队列数据清除成功")

        # 7. 创建关节运动指令（小幅移动第一个关节）
        print("📝 创建关节运动指令...")
        
        # 目标关节角度：第一个关节增加5度
        target_joints = current_joints.copy()
        target_joints[0] += math.radians(5.0)  # 第一个关节增加5度
        
        target_joints_deg = [math.degrees(angle) for angle in target_joints]
        print(f"   目标关节角度: {[f'{angle:.1f}°' for angle in target_joints_deg]}")

        # 8. 按照官方demo的方式创建指令
        print("➕ 添加关节运动指令到队列...")
        
        # 创建位置向量
        pos = nrc.VectorDouble()
        for angle in target_joints:
            pos.append(angle)
        
        # 创建运动命令（完全按照官方demo）
        cmd = nrc.MoveCmd()
        cmd.targetPosType = nrc.PosType_data  # 使用官方常量
        cmd.targetPosValue = pos
        cmd.velocity = 30  # 较慢的速度
        cmd.acc = 50
        cmd.dec = 50
        
        print(f"   🔍 指令详情:")
        print(f"      targetPosType: {cmd.targetPosType}")
        print(f"      targetPosValue: {list(cmd.targetPosValue)}")
        print(f"      velocity: {cmd.velocity}")
        
        # 使用关节运动
        result = nrc.queue_motion_push_back_moveJ(socket_fd, cmd)
        if result != 0:
            print(f"❌ 添加关节指令失败，错误码: {result}")
            return
        print("✅ 关节指令添加成功")

        # 9. 发送队列
        print("📤 发送队列...")
        result = nrc.queue_motion_send_to_controller(socket_fd, 1)
        if result != 0:
            print(f"❌ 发送队列失败，错误码: {result}")
            return
        print("✅ 队列发送成功")

        # 10. 等待执行完成
        print("⏳ 等待执行完成...")
        start_time = time.time()
        while time.time() - start_time < 30:
            try:
                running_status = 0
                result = nrc.get_robot_running_state(socket_fd, running_status)
                if isinstance(result, list) and len(result) > 1:
                    running_status = result[1]
                
                queue_len = 0
                result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
                if isinstance(result, list) and len(result) > 1:
                    queue_len = result[1]
                
                print(f"   运行状态: {running_status}, 队列长度: {queue_len}")
                
                if running_status == 0 and queue_len == 0:
                    print("   ✅ 队列执行完成")
                    break
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"   ⚠️ 检查状态时发生错误: {e}")
                time.sleep(0.5)
        else:
            print("   ⚠️ 等待超时")

        # 11. 检查最终关节角度
        print("📍 检查最终关节角度...")
        final_joint_pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 0, final_joint_pos)
        if result == 0 and len(final_joint_pos) >= 6:
            final_joints = [final_joint_pos[i] for i in range(6)]
            final_joints_deg = [math.degrees(angle) for angle in final_joints]
            print(f"   最终关节角度: {[f'{angle:.1f}°' for angle in final_joints_deg]}")
            
            # 检查第一个关节是否移动了
            joint1_movement = math.degrees(final_joints[0] - current_joints[0])
            print(f"   第一关节移动量: {joint1_movement:.1f}° (预期: 5.0°)")
            
            if abs(joint1_movement - 5.0) < 1.0:
                print("✅ 队列模式关节运动工作正常！")
            else:
                print("❌ 队列模式关节运动有问题")

        # 12. 关闭队列模式
        print("🔒 关闭队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, False)
        if result != 0:
            print(f"⚠️ 关闭队列模式失败，错误码: {result}")
        else:
            print("✅ 队列模式已关闭")

        # 13. 测试直线运动队列
        print("\n" + "=" * 40)
        print("🔄 测试直线运动队列")
        print("=" * 40)

        # 重新启动队列模式
        print("🚀 重新启动队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启动队列模式失败，错误码: {result}")
        else:
            print("✅ 队列模式重新启动成功")

            # 清除队列
            result = nrc.queue_motion_clear_Data(socket_fd)
            if result == 0:
                print("✅ 队列数据清除成功")

                # 获取当前笛卡尔位置
                print("📍 获取当前笛卡尔位置...")
                cart_pos = nrc.VectorDouble()
                result = nrc.get_current_position(socket_fd, 1, cart_pos)  # 1=笛卡尔坐标
                if result == 0 and len(cart_pos) >= 6:
                    current_cart = [cart_pos[i] for i in range(6)]
                    print(f"   当前位置: X={current_cart[0]:.3f}, Y={current_cart[1]:.3f}, Z={current_cart[2]:.3f}")

                    # 创建直线运动指令（Z轴安全移动）
                    target_cart = current_cart.copy()
                    target_cart[2] += 50.0  # Z轴向上50mm（安全距离）

                    print(f"   目标位置: X={target_cart[0]:.3f}, Y={target_cart[1]:.3f}, Z={target_cart[2]:.3f}")

                    # 创建直线运动命令
                    pos_l = nrc.VectorDouble()
                    for val in target_cart:
                        pos_l.append(val)

                    cmd_l = nrc.MoveCmd()
                    cmd_l.targetPosType = nrc.PosType_data  # 使用官方常量
                    cmd_l.targetPosValue = pos_l
                    cmd_l.velocity = 10  # 很低的速度
                    cmd_l.acc = 20       # 很低的加速度
                    cmd_l.dec = 20       # 很低的减速度

                    print(f"   🔍 直线运动指令详情:")
                    print(f"      targetPosType: {cmd_l.targetPosType}")
                    print(f"      targetPosValue: {list(cmd_l.targetPosValue)}")

                    # 添加直线运动到队列
                    result = nrc.queue_motion_push_back_moveL(socket_fd, cmd_l)
                    if result == 0:
                        print("✅ 直线运动指令添加成功")

                        # 发送队列
                        result = nrc.queue_motion_send_to_controller(socket_fd, 1)
                        if result == 0:
                            print("✅ 直线运动队列发送成功")

                            # 等待执行（更长时间，因为速度很慢）
                            print("⏳ 等待直线运动执行...")
                            time.sleep(10)  # 给足够时间完成慢速运动

                            # 检查最终位置
                            final_cart_pos = nrc.VectorDouble()
                            result = nrc.get_current_position(socket_fd, 1, final_cart_pos)
                            if result == 0 and len(final_cart_pos) >= 6:
                                final_cart = [final_cart_pos[i] for i in range(6)]
                                print(f"   最终位置: X={final_cart[0]:.3f}, Y={final_cart[1]:.3f}, Z={final_cart[2]:.3f}")

                                z_movement = final_cart[2] - current_cart[2]
                                print(f"   Z轴移动量: {z_movement:.3f}mm (预期: 50.0mm)")

                                if abs(z_movement - 50.0) < 5.0:
                                    print("✅ 直线运动队列工作正常！")
                                else:
                                    print("❌ 直线运动队列有问题")
                        else:
                            print(f"❌ 发送直线运动队列失败，错误码: {result}")
                    else:
                        print(f"❌ 添加直线运动指令失败，错误码: {result}")

            # 关闭队列模式
            nrc.queue_motion_set_status(socket_fd, False)

        # 14. 设置回示教模式
        result = nrc.set_current_mode(socket_fd, 0)
        if result != 0:
            print(f"⚠️ 设置示教模式失败，错误码: {result}")
        else:
            print("✅ 已切换回示教模式")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        if socket_fd > 0:
            print("🔌 下电并断开连接...")
            nrc.set_servo_poweroff(socket_fd)
            nrc.disconnect_robot(socket_fd)
            print("✅ 测试完成")

if __name__ == "__main__":
    test_queue_with_joint_coords()
