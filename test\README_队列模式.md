# INEXBOT机械臂 G-code队列模式执行程序

## 概述

本程序是基于INEXBOT机械臂的G-code路径执行程序的队列模式版本。相比原版的逐步执行模式，队列模式提供了更平滑、更高效的连续运动执行。

## 队列模式优势

### 🎯 主要优势
- **平滑连续运动**: 减少运动间的停顿，实现更流畅的路径执行
- **提高执行效率**: 批量发送指令，减少通信开销
- **更好的运动规划**: 机械臂可以提前规划整个路径的运动轨迹
- **适合连续路径**: 特别适合3D打印、激光切割、焊接等连续路径应用

### 📊 性能对比
| 特性 | 逐步模式 | 队列模式 |
|------|----------|----------|
| 运动平滑度 | 一般 | 优秀 |
| 执行效率 | 较低 | 高 |
| 通信开销 | 高 | 低 |
| 适用场景 | 精确定位 | 连续路径 |

## 使用方法

### 1. 配置参数

在 `run_gcode.py` 中修改以下参数：

```python
# G-code文件路径
GCODE_FILE = "your_gcode_file.gcode"

# 队列模式参数
QUEUE_BATCH_SIZE = 20  # 批次大小，0表示一次发送所有指令
QUEUE_TIMEOUT_MULTIPLIER = 2  # 超时倍数

# 运动参数
VELOCITY_PERCENT = 50  # 速度百分比
ACCEL_PERCENT = 20     # 加速度百分比
SMOOTHING_LEVEL = 0    # 平滑度 (0-8)
```

### 2. 运行程序

```bash
cd test
python run_gcode.py
```

### 3. 测试队列模式

```bash
# 创建测试G-code文件并获取使用说明
python test_queue_gcode.py
```

## 工作流程

### 队列模式执行流程

1. **连接和初始化**
   - 连接机械臂
   - 检查并上电
   - 设置用户坐标系

2. **队列模式设置**
   - 设置远程模式
   - 启动队列模式
   - 清除队列数据

3. **批量处理路径点**
   - 解析G-code文件
   - 转换坐标和角度
   - 批量添加到队列

4. **执行和监控**
   - 发送队列到控制器
   - 监控执行状态
   - 等待完成

5. **清理和断开**
   - 关闭队列模式
   - 下电并断开连接

## 技术细节

### 队列管理
- 支持批量处理大型G-code文件
- 自动分批发送，避免队列溢出
- 实时监控队列状态和执行进度

### 角度处理
- 支持G-code中的ABC角度
- 自动角度偏移和标准化
- 与原版保持一致的角度处理逻辑

### 错误处理
- 完善的错误检测和恢复机制
- 自动清理队列模式
- 安全的断开连接流程

## 配置说明

### 批次大小 (QUEUE_BATCH_SIZE)
- `0`: 一次性发送所有指令（适合小文件）
- `>0`: 分批发送指令（适合大文件）
- 推荐值: 20-50

### 超时设置
- 队列超时 = 单步超时 × 批次大小 × 超时倍数
- 可根据实际运动时间调整

## 注意事项

1. **硬件要求**
   - 确保机械臂固件支持队列模式
   - 网络连接稳定

2. **G-code兼容性**
   - 支持标准G1直线运动指令
   - 支持XYZ位置和ABC角度

3. **安全考虑**
   - 执行前检查路径安全性
   - 确保工作空间无障碍物
   - 建议先用小文件测试

## 故障排除

### 常见问题

1. **队列模式启动失败**
   - 检查机械臂是否支持队列模式
   - 确认远程模式设置成功

2. **指令添加失败**
   - 检查队列是否已满
   - 验证运动参数是否合理

3. **执行超时**
   - 增加超时倍数
   - 减少批次大小

## 示例文件

- `test_square.gcode`: 简单正方形路径测试文件
- `test_queue_gcode.py`: 队列模式测试脚本

## 参考资料

- `test_simple_queue.py`: 基础队列模式示例
- `Widget.py` 和 `main.py`: 官方GUI demo
- `NRC_完整接口函数文档.md`: 完整API文档
